import { useState } from 'react';
import { Button } from '@blmcp/ui';
import PartialFilterModal from '../lego/components/PartialFilterModal';
import type { FilterCondition } from '../lego/components/PartialFilterModal';

const TestPartialFilter = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [savedConditions, setSavedConditions] = useState<FilterCondition[]>([]);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleSave = (conditions: FilterCondition[]) => {
    setSavedConditions(conditions);
    console.log('保存的筛选条件:', conditions);
  };

  return (
    <div style={{ padding: '20px' }}>
      <h1>局部筛选器测试页面</h1>
      
      <Button type="primary" onClick={handleOpenModal}>
        打开局部筛选器设置
      </Button>

      {savedConditions.length > 0 && (
        <div style={{ marginTop: '20px' }}>
          <h3>已保存的筛选条件:</h3>
          <pre>{JSON.stringify(savedConditions, null, 2)}</pre>
        </div>
      )}

      <PartialFilterModal
        isModalOpen={isModalOpen}
        handleCloseModal={handleCloseModal}
        onSave={handleSave}
        initialData={savedConditions}
      />
    </div>
  );
};

export default TestPartialFilter;

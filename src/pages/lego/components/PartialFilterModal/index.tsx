import { useState } from 'react';
import { <PERSON><PERSON>, Button, message } from '@blmcp/ui';
import './index.less';

interface FilterCondition {
  id: string;
  name: string;
  type: string;
  selectedCharts: string[];
  selectedFields: Record<string, string>;
}

interface PartialFilterModalProps {
  isModalOpen: boolean;
  handleCloseModal: () => void;
  onSave?: (conditions: FilterCondition[]) => void;
}

const PartialFilterModal = ({
  isModalOpen,
  handleCloseModal,
  onSave,
}: PartialFilterModalProps) => {
  return (
    <Modal
      title="查询条件设置"
      open={isModalOpen}
      onCancel={handleCloseModal}
      onOk={handleSave}
      width={1000}
      className="partial-filter-modal"
    ></Modal>
  );
};

export default PartialFilterModal;

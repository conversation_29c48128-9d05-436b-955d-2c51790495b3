import { useState, useCallback, useEffect, useRef } from 'react';
import { Button, Input, Tooltip, BLMIconFont } from '@blmcp/ui';
import { useTooltipShow } from '@/pages/lego/libraryMaterials/hooks/useTooltipShow';
import { FilterCondition } from '../../index';
import './index.less';
import { truncate } from 'lodash';

interface FilterConditionListProps {
  conditions: FilterCondition[];
  activeConditionId: string;
  onAddCondition: () => void;
  onDeleteCondition: (id: string) => void;
  onSelectCondition: (id: string) => void;
  onUpdateConditionName: (id: string, name: string) => void;
}

const FilterConditionList = ({
  conditions,
  activeConditionId,
  onAddCondition,
  onDeleteCondition,
  onSelectCondition,
  onUpdateConditionName,
}: FilterConditionListProps) => {
  // 当前编辑状态的查询条件id
  const [editingId, setEditingId] = useState<string>('');
  // 当前编辑状态的查询条件名称
  const [editingName, setEditingName] = useState<string>('');

  // 开始编辑名称
  const handleStartEdit = useCallback((condition: FilterCondition) => {
    setEditingId(condition.id);
    setEditingName(condition.name);
  }, []);

  // 完成编辑
  const handleFinishEdit = useCallback(() => {
    if (editingId && editingName.trim()) {
      onUpdateConditionName(editingId, editingName.trim());
    }
    setEditingId('');
    setEditingName('');
  }, [editingId, editingName, onUpdateConditionName]);

  // 取消编辑
  const handleCancelEdit = useCallback(() => {
    setEditingId('');
    setEditingName('');
  }, []);

  // 输入验证
  const validateInput = useCallback((value: string) => {
    // 限制中文、英文字母、中英文（）、数字，48字符限制
    const regex = /^[\u4e00-\u9fa5a-zA-Z0-9（）()]*$/;
    return regex.test(value) && value.length <= 48;
  }, []);

  // 处理输入变化
  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      if (validateInput(value)) {
        setEditingName(value);
      }
    },
    [validateInput],
  );

  // 处理键盘事件
  const handleKeyPress = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Enter') {
        handleFinishEdit();
      } else if (e.key === 'Escape') {
        handleCancelEdit();
      }
    },
    [handleFinishEdit, handleCancelEdit],
  );
  // const { tooltipEnable, textRef } = useTooltipShow('');
  console.log(conditions, 'conditions----error----');
  return (
    <div className="filter-condition-list">
      <div className="condition-header">
        <span className="header-title">查询条件</span>
        <Tooltip
          title={
            conditions.length >= 5 ? '局部筛选器最多支持5个' : '新增查询条件'
          }
        >
          <Button
            type="text"
            icon={<BLMIconFont type="BLM-ic-plus-o" />}
            onClick={onAddCondition}
            disabled={conditions.length >= 5}
            className="add-condition-btn"
          />
        </Tooltip>
      </div>

      <div className="condition-list">
        {conditions.map((condition) => (
          <div
            key={condition.id}
            className={`condition-item ${
              activeConditionId === condition.id ? 'active' : ''
            } ${condition.isValid === false ? 'error' : ''}`}
            onClick={() => onSelectCondition(condition.id)}
          >
            <div className="condition-content">
              {editingId === condition.id ? (
                <Input
                  value={editingName}
                  onChange={handleInputChange}
                  onBlur={handleFinishEdit}
                  onKeyDown={handleKeyPress}
                  autoFocus
                  className="condition-input"
                  maxLength={48}
                />
              ) : (
                <div className="condition-name-wrapper">
                  {condition.isValid === false && (
                    <Tooltip
                      title={
                        <div style={{ whiteSpace: 'pre-line' }}>
                          {condition.errorMessage?.map((msg, i) => (
                            <div key={i}>{msg}</div>
                          ))}
                        </div>
                      }
                    >
                      <BLMIconFont
                        className="error-icon"
                        type="BLM-ic-error-o"
                      />
                    </Tooltip>
                  )}
                  <Tooltip
                    title={condition.name}
                  >
                    <span
                      className="condition-name"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleStartEdit(condition);
                      }}
                    >
                      {condition.name}
                    </span>
                  </Tooltip>
                </div>
              )}

              <Button
                type="text"
                icon={<BLMIconFont type="BLM-ic-delete-o" />}
                onClick={(e) => {
                  e.stopPropagation();
                  onDeleteCondition(condition.id);
                }}
                className="delete-btn"
                size="small"
              />
            </div>
          </div>
        ))}

        {conditions.length === 0 && (
          <div className="empty-state">
            <span>暂无查询条件</span>
            <span>点击上方 + 号添加</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default FilterConditionList;

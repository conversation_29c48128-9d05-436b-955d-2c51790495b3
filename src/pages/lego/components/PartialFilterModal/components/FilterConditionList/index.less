.filter-condition-list {
  height: 100%;
  display: flex;
  flex-direction: column;

  .condition-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;

    .header-title {
      font-weight: 500;
      color: #262626;
    }

    .add-condition-btn {
      padding: 4px;
      border-radius: 4px;

      &:hover:not(:disabled) {
        background-color: #f5f5f5;
      }

      &:disabled {
        color: #d9d9d9;
        cursor: not-allowed;
      }
    }
  }

  .condition-list {
    flex: 1;
    overflow-y: auto;

    .condition-item {
      margin-bottom: 8px;
      padding: 8px 12px;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        border-color: #40a9ff;
        background-color: #f6ffed;
      }

      &.active {
        border-color: #1890ff;
        background-color: #e6f7ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }

      &.error {
        border-color: #ff4d4f;
        background-color: #fff2f0;

        &:hover {
          border-color: #ff4d4f;
          background-color: #fff2f0;
        }

        &.active {
          border-color: #ff4d4f;
          background-color: #fff2f0;
          box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
        }
      }

      .condition-content {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .condition-input {
          flex: 1;
          margin-right: 8px;
        }

        .condition-name-wrapper {
          flex: 1;
          display: flex;
          align-items: center;
          gap: 4px;

          .error-icon {
            color: #ff4d4f;
            font-size: 12px;
          }

          .condition-name {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            cursor: text;
            // max-width: 120px;

            &:hover {
              text-decoration: underline;
            }
          }
        }

        .delete-btn {
          opacity: 0;
          transition: opacity 0.2s;
          color: #ff4d4f;

          &:hover {
            background-color: rgba(255, 77, 79, 0.1);
          }
        }
      }

      &:hover .delete-btn {
        opacity: 1;
      }
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200px;
      color: #8c8c8c;
      font-size: 14px;
      gap: 8px;
    }
  }
}

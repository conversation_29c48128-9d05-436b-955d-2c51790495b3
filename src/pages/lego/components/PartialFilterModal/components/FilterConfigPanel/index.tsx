import { useCallback } from 'react';
import { Radio, Alert } from '@blmcp/ui';
import { FilterCondition } from '../../index';
import ChartSelectionTable from '../ChartSelectionTable';
import './index.less';

interface FilterConfigPanelProps {
  condition?: FilterCondition;
  onUpdateCondition: (id: string, updates: Partial<FilterCondition>) => void;
}

const FilterConfigPanel = ({
  condition,
  onUpdateCondition,
}: FilterConfigPanelProps) => {
  // 处理筛选器类型变化
  const handleTypeChange = useCallback(
    (e: any) => {
      if (condition) {
        onUpdateCondition(condition.id, {
          type: e.target.value,
          // 切换类型时清空已选择的图表和字段
          selectedCharts: [],
          selectedFields: {},
        });
      }
    },
    [condition, onUpdateCondition],
  );

  // 处理图表选择变化
  const handleChartSelectionChange = useCallback(
    (selectedCharts: string[], selectedFields: Record<string, string>) => {
      if (condition) {
        onUpdateCondition(condition.id, {
          selectedCharts,
          selectedFields,
        });
      }
    },
    [condition, onUpdateCondition],
  );

  if (!condition) {
    return (
      <div className="filter-config-panel">
        <div className="empty-config">
          <div className="empty-icon">📋</div>
          <div className="empty-text">请先新建查询条件</div>
        </div>
      </div>
    );
  }

  return (
    <div className="filter-config-panel">
      <div className="config-section">
        <div className="section-title">筛选器类型</div>
        <Radio.Group
          value={condition.type}
          onChange={handleTypeChange}
          className="filter-type-radio"
        >
          <Radio value="time">时间筛选器</Radio>
          <Radio value="range">区间筛选器</Radio>
          <Radio value="text">文本筛选器</Radio>
          <Radio value="list">列表筛选器</Radio>
        </Radio.Group>

        {condition.type === 'list' && (
          <Alert
            message="列表筛选器仅支持配置同数据图表。"
            type="info"
            showIcon
            className="list-filter-alert"
          />
        )}
      </div>

      <div className="config-section">
        <div className="section-title">关联图表</div>
        <ChartSelectionTable
          selectedCharts={condition.selectedCharts}
          selectedFields={condition.selectedFields}
          onSelectionChange={handleChartSelectionChange}
        />
      </div>
    </div>
  );
};

export default FilterConfigPanel;

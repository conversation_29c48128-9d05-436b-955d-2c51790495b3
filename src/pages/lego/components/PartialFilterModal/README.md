# 局部筛选器弹窗组件

## 功能概述

这是一个完整的局部筛选器设置弹窗组件，支持用户创建和配置多个筛选条件。

## 主要功能

### 1. 左侧查询条件管理
- ✅ 支持添加最多5个查询条件
- ✅ 达到5个时新建入口置灰，hover提示"局部筛选器最多支持5个"
- ✅ 每个条件默认名称为"查询条件1"、"查询条件2"等，根据数量自动累加
- ✅ 支持点击条件名称进行编辑，限制中文、英文字母、中英文（）、数字，48字符限制
- ✅ 输入完成后鼠标失焦保存，hover可展示完整名称tips
- ✅ 每个条件带有删除按钮，点击可删除当前查询条件
- ✅ 支持点击切换不同查询条件，右侧显示对应配置

### 2. 右侧配置面板
- ✅ 无查询条件时显示"请先新建查询条件"提示
- ✅ 筛选器类型单选框：时间筛选器、区间筛选器、文本筛选器、列表筛选器
- ✅ 默认选中时间筛选器
- ✅ 选中列表筛选器时显示提示："列表筛选器仅支持配置同数据图表。"

### 3. 图表选择表格
- ✅ 表格展示图表列表，支持复选框多选
- ✅ 表格包含：图表名称、图表类型、数据源、关联字段列
- ✅ 支持全选/取消全选功能
- ✅ 关联字段下拉框：未选中图表时置灰，选中后可交互
- ✅ Mock数据包含5个示例图表和对应字段选项

### 4. 数据验证和保存
- ✅ 保存时验证配置完整性：
  - 筛选器类型必须选择
  - 必须选择至少一个图表
  - 选中的图表必须配置关联字段
- ✅ 验证筛选器名称不能重复
- ✅ 验证失败时显示错误提示和叹号图标，hover显示具体错误信息
- ✅ 支持同时显示多个错误信息

### 5. 数据回填
- ✅ 支持通过initialData属性传入已保存的筛选条件
- ✅ 再次打开弹窗时自动回填之前的配置

## 组件结构

```
PartialFilterModal/
├── index.tsx                    # 主弹窗组件
├── index.less                   # 主样式文件
├── components/
│   ├── FilterConditionList/     # 左侧查询条件列表
│   │   ├── index.tsx
│   │   └── index.less
│   ├── FilterConfigPanel/       # 右侧配置面板
│   │   ├── index.tsx
│   │   └── index.less
│   └── ChartSelectionTable/     # 图表选择表格
│       ├── index.tsx
│       └── index.less
└── README.md                    # 说明文档
```

## 使用方式

```tsx
import PartialFilterModal from './components/PartialFilterModal';
import type { FilterCondition } from './components/PartialFilterModal';

const [isModalOpen, setIsModalOpen] = useState(false);
const [conditions, setConditions] = useState<FilterCondition[]>([]);

<PartialFilterModal
  isModalOpen={isModalOpen}
  handleCloseModal={() => setIsModalOpen(false)}
  onSave={(conditions) => setConditions(conditions)}
  initialData={conditions}
/>
```

## 数据结构

```typescript
interface FilterCondition {
  id: string;                           // 唯一标识
  name: string;                         // 筛选器名称
  type: 'time' | 'range' | 'text' | 'list'; // 筛选器类型
  selectedCharts: string[];             // 选中的图表ID列表
  selectedFields: Record<string, string>; // 图表ID到字段的映射
  isValid?: boolean;                    // 验证状态
  errorMessage?: string;                // 错误信息
}
```

## 技术特点

- 使用Antd组件库进行UI构建
- 组件化设计，职责分离清晰
- 完整的TypeScript类型定义
- 响应式布局设计
- 完善的错误处理和用户提示
- 支持数据回填和状态管理

import { useRef, useState } from 'react';
import { Button } from '@blmcp/ui';
import { blmAnalysisModuleClick } from '@/utils/eventTracking';
import relationCenterExp from '../../libraryMaterials/module/RelationCenter';
import './index.less';
import linkageCenterExp from '../../libraryMaterials/module/Linkage';
import queryCenterExp from '@/pages/lego/libraryMaterials/module/Query';
import { globalCache } from '@/pages/lego/utils/cache';
import isMobile from '../../utils/isMobile';
import PartialFilterModal from '../PartialFilterModal';
import Icon from './Icon.jsx';

const waitTime = 1000;

export const SearchButton = (props) => {
  const linkageCenter = linkageCenterExp(props.uuid);
  const queryCenter = queryCenterExp(props.uuid);
  const relationCenter = relationCenterExp(props.uuid);
  const [loading, setLoading] = useState(false);
  const [expandText, setExpandText] = useState('展开筛选项');
  const [isModalOpen, setIsModalOpen] = useState(false);
  // const search = () => {
  //   setLoading(true);
  //   setTimeout(() => {
  //     setLoading(false);
  //   }, waitTime);
  //   queryCenter.resetTableQuery();
  //   relationCenter.notify('all');
  //   // TODO 后续新增图表需要配置类型 筛选器的数据不清空
  //   globalCache.clear([1, 2, 3, 5]);
  //   blmAnalysisModuleClick({
  //     eventId: 'e_leopard_cp_click_00003812',
  //     pageId: 'p_leopard_cp_00000884',
  //     ext: {
  //       str0_e: props.uuid,
  //     },
  //   });
  // };
  const search = () => {
    setIsModalOpen(true);
  };
  const handleCloseModal = () => {
    setIsModalOpen(false);
  };
  const reset = () => {
    // 重置，走联动规则
    linkageCenter.notify('reset');
    // TODO 后续新增图表需要配置类型 筛选器的数据不清空
    globalCache.clear([1, 2, 3, 5]);
    queryCenter.resetTableQuery();
    relationCenter.notify('all');
  };
  const wrapRef = useRef();
  const onExpand = () => {
    wrapRef.current?.parentNode?.parentNode?.classList?.toggle('expand');
    setExpandText(expandText === '展开筛选项' ? '收起筛选项' : '展开筛选项');
  };
  // 局部筛选器保存函数
  const onSave = (conditions) => {
    console.log('conditions', conditions);
    // 保存，走联动规则
    // linkageCenter.notify('save');
    // queryCenter.resetTableQuery();
    // relationCenter.notify('all');
  };
  return (
    <div
      className={`lego-search-wrap ${isMobile() ? 'isMobile' : ''}`}
      ref={wrapRef}
    >
      <Button className="lego-search-btn" onClick={reset}>
        重置
      </Button>
      <Button type="primary" onClick={search} loading={loading}>
        查询
      </Button>
      {isMobile() ? (
        <div className="condition-expand-icon" onClick={onExpand}>
          <span style={{ marginRight: '5px' }}>{expandText}</span>
          <Icon></Icon>
        </div>
      ) : null}
      <PartialFilterModal
        isModalOpen={isModalOpen}
        handleCloseModal={handleCloseModal}
        onSave={onSave}
      ></PartialFilterModal>
    </div>
  );
};

.partial-filter-modal {
  .ant-modal-body {
    padding: 0;
  }
}

.partial-filter-content {
  display: flex;
  height: 600px;

  .filter-sidebar {
    width: 25%;
    border-right: 1px solid #f0f0f0;
    padding: 16px;
    background-color: #fafafa;

    .sidebar-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .sidebar-title {
        font-weight: 500;
        color: #262626;
      }

      .add-button {
        padding: 4px;
        border: none;
        background: none;
        cursor: pointer;
        color: #1890ff;

        &:hover {
          color: #40a9ff;
        }

        &:disabled {
          color: #d9d9d9;
          cursor: not-allowed;
        }
      }
    }

    .filter-tabs {
      .ant-tabs-nav {
        margin-bottom: 0;
      }

      .ant-tabs-tab {
        padding: 8px 12px;

        .ant-tabs-tab-btn {
          font-size: 12px;
        }
      }

      .ant-tabs-content-holder {
        display: none;
      }
    }
  }

  .filter-main {
    flex: 1;
    padding: 24px;

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #8c8c8c;
      font-size: 14px;
    }

    .config-panel {
      .filter-type-section {
        margin-bottom: 24px;

        .section-title {
          font-weight: 500;
          margin-bottom: 12px;
          color: #262626;
        }

        .list-filter-notice {
          margin-top: 8px;
          color: #8c8c8c;
          font-size: 12px;
        }
      }

      .chart-selection-section {
        .chart-groups {
          .chart-group {
            margin-bottom: 16px;
            border: 1px solid #f0f0f0;
            border-radius: 6px;

            .group-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 12px 16px;
              background-color: #fafafa;
              border-bottom: 1px solid #f0f0f0;

              .group-title {
                font-weight: 500;
                color: #262626;
              }

              .group-actions {
                display: flex;
                gap: 8px;

                .action-button {
                  padding: 4px 8px;
                  border: 1px solid #d9d9d9;
                  background: white;
                  border-radius: 4px;
                  cursor: pointer;
                  font-size: 12px;

                  &:hover {
                    border-color: #1890ff;
                    color: #1890ff;
                  }

                  &:disabled {
                    color: #d9d9d9;
                    border-color: #f0f0f0;
                    cursor: not-allowed;
                  }
                }
              }
            }

            .group-content {
              padding: 16px;

              &.disabled {
                background-color: #f5f5f5;

                .chart-item {
                  color: #bfbfbf;
                  cursor: not-allowed;
                }
              }

              .chart-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 8px 0;
                border-bottom: 1px solid #f0f0f0;

                &:last-child {
                  border-bottom: none;
                }

                .chart-info {
                  display: flex;
                  align-items: center;
                  flex: 1;

                  .chart-checkbox {
                    margin-right: 12px;
                  }

                  .chart-name {
                    font-size: 14px;
                    color: #262626;
                  }
                }

                .field-select {
                  width: 200px;
                }
              }
            }
          }
        }
      }
    }
  }
}

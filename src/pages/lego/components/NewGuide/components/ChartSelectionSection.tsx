import { useState, useEffect } from 'react';
import { Checkbox, Button, Select, Tooltip } from '@blmcp/ui';
import { mockChartData, mockFieldOptions } from '../utils/mockData';

interface FilterCondition {
  id: string;
  name: string;
  type: 'time' | 'range' | 'text' | 'list';
  selectedCharts: string[];
  selectedFields: { [chartId: string]: string };
}

interface ChartGroup {
  datasetId: string;
  datasetName: string;
  charts: Array<{
    id: string;
    name: string;
  }>;
}

interface ChartSelectionSectionProps {
  condition: FilterCondition;
  onConditionUpdate: (updates: Partial<FilterCondition>) => void;
}

const ChartSelectionSection = ({
  condition,
  onConditionUpdate,
}: ChartSelectionSectionProps) => {
  const [chartGroups] = useState<ChartGroup[]>(mockChartData);
  const [disabledGroups, setDisabledGroups] = useState<string[]>([]);

  useEffect(() => {
    if (condition.type === 'list' && condition.selectedCharts.length > 0) {
      // 找到已选图表所属的数据集
      const selectedDatasets = new Set<string>();
      chartGroups.forEach((group) => {
        if (
          group.charts.some((chart) =>
            condition.selectedCharts.includes(chart.id),
          )
        ) {
          selectedDatasets.add(group.datasetId);
        }
      });

      // 禁用其他数据集
      const disabled = chartGroups
        .filter((group) => !selectedDatasets.has(group.datasetId))
        .map((group) => group.datasetId);

      setDisabledGroups(disabled);
    } else {
      setDisabledGroups([]);
    }
  }, [condition.type, condition.selectedCharts, chartGroups]);

  const handleChartSelect = (chartId: string, checked: boolean) => {
    let newSelectedCharts: string[];
    let newSelectedFields = { ...condition.selectedFields };

    if (checked) {
      newSelectedCharts = [...condition.selectedCharts, chartId];
    } else {
      newSelectedCharts = condition.selectedCharts.filter(
        (id) => id !== chartId,
      );
      delete newSelectedFields[chartId];
    }

    onConditionUpdate({
      selectedCharts: newSelectedCharts,
      selectedFields: newSelectedFields,
    });
  };

  const handleGroupSelectAll = (groupId: string, selectAll: boolean) => {
    const group = chartGroups.find((g) => g.datasetId === groupId);
    if (!group) return;

    let newSelectedCharts = [...condition.selectedCharts];
    let newSelectedFields = { ...condition.selectedFields };

    if (selectAll) {
      // 添加该组所有图表
      group.charts.forEach((chart) => {
        if (!newSelectedCharts.includes(chart.id)) {
          newSelectedCharts.push(chart.id);
        }
      });
    } else {
      // 移除该组所有图表
      group.charts.forEach((chart) => {
        newSelectedCharts = newSelectedCharts.filter((id) => id !== chart.id);
        delete newSelectedFields[chart.id];
      });
    }

    onConditionUpdate({
      selectedCharts: newSelectedCharts,
      selectedFields: newSelectedFields,
    });
  };

  const handleFieldSelect = (chartId: string, fieldValue: string) => {
    onConditionUpdate({
      selectedFields: {
        ...condition.selectedFields,
        [chartId]: fieldValue,
      },
    });
  };

  const isGroupSelected = (groupId: string) => {
    const group = chartGroups.find((g) => g.datasetId === groupId);
    if (!group) return false;
    return group.charts.every((chart) =>
      condition.selectedCharts.includes(chart.id),
    );
  };

  const isGroupPartiallySelected = (groupId: string) => {
    const group = chartGroups.find((g) => g.datasetId === groupId);
    if (!group) return false;
    const selectedCount = group.charts.filter((chart) =>
      condition.selectedCharts.includes(chart.id),
    ).length;
    return selectedCount > 0 && selectedCount < group.charts.length;
  };

  return (
    <div className="chart-groups">
      {chartGroups.map((group) => {
        const isDisabled = disabledGroups.includes(group.datasetId);
        const isSelected = isGroupSelected(group.datasetId);
        const isPartiallySelected = isGroupPartiallySelected(group.datasetId);

        return (
          <div key={group.datasetId} className="chart-group">
            <div className="group-header">
              <div className="group-title">{group.datasetName}</div>
              <div className="group-actions">
                <Button
                  size="small"
                  onClick={() => handleGroupSelectAll(group.datasetId, true)}
                  disabled={isDisabled || isSelected}
                  className="action-button"
                >
                  全选
                </Button>
                <Button
                  size="small"
                  onClick={() => handleGroupSelectAll(group.datasetId, false)}
                  disabled={isDisabled || (!isSelected && !isPartiallySelected)}
                  className="action-button"
                >
                  取消选择
                </Button>
              </div>
            </div>

            <div className={`group-content ${isDisabled ? 'disabled' : ''}`}>
              {group.charts.map((chart) => {
                const isChartSelected = condition.selectedCharts.includes(
                  chart.id,
                );
                const selectedField = condition.selectedFields[chart.id];

                return (
                  <div key={chart.id} className="chart-item">
                    <div className="chart-info">
                      <Tooltip
                        title={
                          isDisabled ? '自定义筛选仅支持单个数据集图表配置' : ''
                        }
                      >
                        <Checkbox
                          checked={isChartSelected}
                          onChange={(e) =>
                            handleChartSelect(chart.id, e.target.checked)
                          }
                          disabled={isDisabled}
                          className="chart-checkbox"
                        />
                      </Tooltip>
                      <span className="chart-name">{chart.name}</span>
                    </div>

                    {isChartSelected && (
                      <Select
                        value={selectedField}
                        onChange={(value) => handleFieldSelect(chart.id, value)}
                        placeholder="请选择字段"
                        className="field-select"
                        options={mockFieldOptions}
                      />
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ChartSelectionSection;
